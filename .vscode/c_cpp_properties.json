{"configurations": [{"browse": {"databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/ros2_ws/install/yaets/include/**", "/home/<USER>/ros2_ws/install/service_test_pkg/include/**", "/home/<USER>/ros2_ws/install/play_motion2/include/**", "/home/<USER>/ros2_ws/install/play_motion2_msgs/include/**", "/home/<USER>/ros2_ws/install/opennav_docking/include/**", "/home/<USER>/ros2_ws/install/opennav_docking_core/include/**", "/home/<USER>/ros2_ws/install/opennav_docking_bt/include/**", "/home/<USER>/ros2_ws/install/dwb_plugins/include/**", "/home/<USER>/ros2_ws/install/dwb_critics/include/**", "/home/<USER>/ros2_ws/install/dwb_core/include/**", "/home/<USER>/ros2_ws/install/nav_2d_utils/include/**", "/home/<USER>/ros2_ws/install/dwb_msgs/include/**", "/home/<USER>/ros2_ws/install/nav_2d_msgs/include/**", "/home/<USER>/ros2_ws/install/nav2_waypoint_follower/include/**", "/home/<USER>/ros2_ws/install/nav2_theta_star_planner/include/**", "/home/<USER>/ros2_ws/install/nav2_smoother/include/**", "/home/<USER>/ros2_ws/install/nav2_smac_planner/include/**", "/home/<USER>/ros2_ws/install/nav2_rviz_plugins/include/**", "/home/<USER>/ros2_ws/install/nav2_route/include/**", "/home/<USER>/ros2_ws/install/nav2_rotation_shim_controller/include/**", "/home/<USER>/ros2_ws/install/nav2_regulated_pure_pursuit_controller/include/**", "/home/<USER>/ros2_ws/install/nav2_planner/include/**", "/home/<USER>/ros2_ws/install/nav2_navfn_planner/include/**", "/home/<USER>/ros2_ws/install/nav2_mppi_controller/include/**", "/home/<USER>/ros2_ws/install/nav2_graceful_controller/include/**", "/home/<USER>/ros2_ws/install/nav2_controller/include/**", "/home/<USER>/ros2_ws/install/nav2_constrained_smoother/include/**", "/home/<USER>/ros2_ws/install/nav2_bt_navigator/include/**", "/home/<USER>/ros2_ws/install/nav2_behaviors/include/**", "/home/<USER>/ros2_ws/install/nav2_core/include/**", "/home/<USER>/ros2_ws/install/nav2_collision_monitor/include/**", "/home/<USER>/ros2_ws/install/costmap_queue/include/**", "/home/<USER>/ros2_ws/install/nav2_costmap_2d/include/**", "/home/<USER>/ros2_ws/install/nav2_voxel_grid/include/**", "/home/<USER>/ros2_ws/install/nav2_velocity_smoother/include/**", "/home/<USER>/ros2_ws/install/nav2_map_server/include/**", "/home/<USER>/ros2_ws/install/nav2_lifecycle_manager/include/**", "/home/<USER>/ros2_ws/install/nav2_behavior_tree/include/**", "/home/<USER>/ros2_ws/install/nav2_amcl/include/**", "/home/<USER>/ros2_ws/install/nav2_util/include/**", "/home/<USER>/ros2_ws/install/nav2_ros_common/include/**", "/home/<USER>/ros2_ws/install/br2_bt_patrolling/include/**", "/home/<USER>/ros2_ws/install/nav2_msgs/include/**", "/home/<USER>/ros2_ws/install/br2_tracking/include/**", "/home/<USER>/ros2_ws/install/br2_tracking_msgs/include/**", "/home/<USER>/ros2_ws/install/br2_bt_bumpgo/include/**", "/opt/ros/jazzy/include/**", "/home/<USER>/ros2_ws/src/testz/include/**", "/usr/include/**"], "name": "ros2", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++17"}], "version": 4}